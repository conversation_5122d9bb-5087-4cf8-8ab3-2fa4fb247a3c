<?php
/**
 * Clients Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and search parameters
$client_manager = new SI_Client();
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get clients with search
$clients = $client_manager->si_get_clients(array(
    'search' => $search,
    'limit' => $per_page,
    'offset' => $offset
));

// Get total count for pagination
$total_clients = count($client_manager->si_get_clients(array('search' => $search)));
$total_pages = ceil($total_clients / $per_page);

// Get statistics
$all_clients = $client_manager->si_get_clients();
$total_count = count($all_clients);

// Get invoice manager for client statistics
$invoice_manager = new SI_Invoice();
$client_stats = array();
$active_clients = 0;
$total_revenue = 0;

foreach ($all_clients as $client) {
    $client_invoices = $invoice_manager->si_get_invoices(array('client_id' => $client->id));
    $client_revenue = array_sum(array_map(function($inv) { return floatval($inv->total_amount); }, $client_invoices));

    $client_stats[$client->id] = array(
        'total_invoices' => count($client_invoices),
        'total_amount' => $client_revenue,
        'last_invoice' => !empty($client_invoices) ? max(array_map(function($inv) { return strtotime($inv->created_at); }, $client_invoices)) : null
    );

    if (count($client_invoices) > 0) {
        $active_clients++;
    }
    $total_revenue += $client_revenue;
}

// Set up page header variables
$page_title = __('Clients', 'wp-invoice-manager-pro');
$page_subtitle = __('Manage your client relationships and contact information', 'wp-invoice-manager-pro');
$page_icon = 'dashicons-groups';
$header_actions = array(
    array(
        'type' => 'button',
        'text' => __('Add New Client', 'wp-invoice-manager-pro'),
        'icon' => 'dashicons-plus-alt',
        'class' => 'si-btn si-btn-primary',
        'id' => 'si-add-client-btn'
    )
);

// Include common header
include WIMP_PLUGIN_PATH . 'admin/views/common/page-header.php';
?>

    <!-- Statistics Overview -->
    <div class="si-dashboard-stats">
        <h2><?php echo esc_html__('Client Overview', 'simple-invoice'); ?></h2>

        <div class="si-stats-grid">
            <!-- Total Clients -->
            <div class="si-stat-card si-stat-clients">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-groups"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($total_count); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Clients', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('All clients', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Active Clients -->
            <div class="si-stat-card si-stat-active">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-yes"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html($active_clients); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Active Clients', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('With invoices', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="si-stat-card si-stat-revenue">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-chart-line"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($total_revenue)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Total Revenue', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('From all clients', 'simple-invoice'); ?></div>
                </div>
            </div>

            <!-- Average per Client -->
            <div class="si-stat-card si-stat-average">
                <div class="si-stat-icon">
                    <span class="dashicons dashicons-calculator"></span>
                </div>
                <div class="si-stat-content">
                    <div class="si-stat-number"><?php echo esc_html(si_format_currency($active_clients > 0 ? $total_revenue / $active_clients : 0)); ?></div>
                    <div class="si-stat-label"><?php echo esc_html__('Avg per Client', 'simple-invoice'); ?></div>
                    <div class="si-stat-meta"><?php echo esc_html__('Revenue average', 'simple-invoice'); ?></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="si-search-section">
        <form method="get" action="">
            <input type="hidden" name="page" value="wimp-clients" />
            <div class="si-search-wrapper">
                <input type="text"
                       name="search"
                       value="<?php echo esc_attr($search); ?>"
                       placeholder="<?php echo esc_attr__('Search clients by name, email, or business...', 'simple-invoice'); ?>"
                       class="si-search-input" />
                <button type="submit" class="si-btn si-btn-secondary">
                    <span class="dashicons dashicons-search"></span>
                    <?php echo esc_html__('Search', 'simple-invoice'); ?>
                </button>
                <?php if (!empty($search)): ?>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-ghost">
                        <span class="dashicons dashicons-no"></span>
                        <?php echo esc_html__('Clear', 'simple-invoice'); ?>
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Clients Table -->
    <div class="si-clients-table-section">
        <?php if (!empty($clients)): ?>
            <div class="si-table-container">
                <table class="si-table si-clients-table">
                    <thead>
                        <tr>
                            <th><?php echo esc_html__('Client', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Contact', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Invoices', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Revenue', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Last Invoice', 'simple-invoice'); ?></th>
                            <th><?php echo esc_html__('Actions', 'simple-invoice'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($clients as $client): ?>
                            <?php
                            $stats = $client_stats[$client->id] ?? array('total_invoices' => 0, 'total_amount' => 0, 'last_invoice' => null);
                            ?>
                            <tr>
                                <td>
                                    <div class="si-client-info">
                                        <div class="si-client-name">
                                            <strong><?php echo esc_html($client->name); ?></strong>
                                        </div>
                                        <?php if (!empty($client->business_name)): ?>
                                            <div class="si-client-business">
                                                <?php echo esc_html($client->business_name); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="si-client-contact">
                                        <?php if (!empty($client->email)): ?>
                                            <div class="si-client-email">
                                                <span class="si-contact-icon">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                                        <polyline points="22,6 12,13 2,6"></polyline>
                                                    </svg>
                                                </span>
                                                <a href="mailto:<?php echo esc_attr($client->email); ?>"><?php echo esc_html($client->email); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->phone)): ?>
                                            <div class="si-client-phone">
                                                <span class="si-contact-icon">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                                    </svg>
                                                </span>
                                                <?php echo esc_html($client->phone); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="si-invoice-count"><?php echo esc_html($stats['total_invoices']); ?></span>
                                </td>
                                <td>
                                    <span class="si-revenue-amount"><?php echo esc_html(si_format_currency($stats['total_amount'])); ?></span>
                                </td>
                                <td>
                                    <?php if ($stats['last_invoice']): ?>
                                        <span class="si-last-invoice"><?php echo esc_html(date('M j, Y', $stats['last_invoice'])); ?></span>
                                    <?php else: ?>
                                        <span class="si-no-invoices"><?php echo esc_html__('No invoices', 'simple-invoice'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="si-client-actions">
                                        <button type="button" class="si-btn si-btn-sm si-btn-secondary si-edit-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Edit Client', 'simple-invoice'); ?>">
                                            <span class="si-btn-icon">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                                </svg>
                                            </span>
                                            <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                        </button>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-create-invoice&client_id=' . $client->id)); ?>" class="si-btn si-btn-sm si-btn-primary" title="<?php echo esc_attr__('Create Invoice', 'simple-invoice'); ?>">
                                            <span class="si-btn-icon">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                    <polyline points="14,2 14,8 20,8"></polyline>
                                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                                    <polyline points="10,9 9,9 8,9"></polyline>
                                                </svg>
                                            </span>
                                            <?php echo esc_html__('Invoice', 'simple-invoice'); ?>
                                        </a>
                                        <button type="button" class="si-btn si-btn-sm si-btn-danger si-delete-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Delete Client', 'simple-invoice'); ?>">
                                            <span class="si-btn-icon">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <polyline points="3,6 5,6 21,6"></polyline>
                                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                                </svg>
                                            </span>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="si-pagination">
                    <?php
                    $pagination_args = array(
                        'base' => add_query_arg('paged', '%#%'),
                        'format' => '',
                        'current' => $paged,
                        'total' => $total_pages,
                        'prev_text' => '&laquo; ' . __('Previous', 'simple-invoice'),
                        'next_text' => __('Next', 'simple-invoice') . ' &raquo;'
                    );
                    echo paginate_links($pagination_args);
                    ?>
                </div>
            <?php endif; ?>

        <?php else: ?>
            <!-- Empty State -->
            <div class="si-empty-state">
                <div class="si-empty-icon">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                </div>
                <h3><?php echo esc_html__('No Clients Found', 'simple-invoice'); ?></h3>
                <?php if (!empty($search)): ?>
                    <p><?php echo esc_html__('No clients match your search criteria. Try a different search term or add a new client.', 'simple-invoice'); ?></p>
                    <a href="<?php echo esc_url(admin_url('admin.php?page=wimp-clients')); ?>" class="si-btn si-btn-secondary">
                        <span class="si-btn-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="19" y1="12" x2="5" y2="12"></line>
                                <polyline points="12,19 5,12 12,5"></polyline>
                            </svg>
                        </span>
                        <?php echo esc_html__('View All Clients', 'simple-invoice'); ?>
                    </a>
                <?php else: ?>
                    <p><?php echo esc_html__('Start building your client database by adding your first client. Clients are essential for creating and managing invoices.', 'simple-invoice'); ?></p>
                <?php endif; ?>
                <button type="button" class="si-btn si-btn-primary" id="si-add-first-client">
                    <span class="si-btn-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                    </span>
                    <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
                </button>
            </div>
        <?php endif; ?>
    </div>

<!-- Add/Edit Client Modal -->
<div id="si-client-modal" class="si-modal" style="display: none;">
    <div class="si-modal-content">
        <div class="si-modal-header">
            <h2 id="si-client-modal-title">
                <span class="si-modal-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </span>
                <?php echo esc_html__('Add New Client', 'simple-invoice'); ?>
            </h2>
            <button type="button" class="si-modal-close">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <div class="si-modal-body">
            <form id="si-client-form" class="si-form">
                <input type="hidden" id="si-client-id" name="client_id" value="" />

                <div class="si-form-grid si-grid-2">
                    <div class="si-form-field">
                        <label for="si-client-name" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Full Name', 'simple-invoice'); ?></span>
                            <span class="si-required">*</span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </span>
                            <input type="text"
                                   id="si-client-name"
                                   name="name"
                                   class="si-form-input"
                                   required
                                   placeholder="<?php echo esc_attr__('Enter client full name', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-business" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M3 21h18"></path>
                                    <path d="M5 21V7l8-4v18"></path>
                                    <path d="M19 21V11l-6-4"></path>
                                </svg>
                            </span>
                            <input type="text"
                                   id="si-client-business"
                                   name="business_name"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Business or company name', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-email" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Email Address', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                    <polyline points="22,6 12,13 2,6"></polyline>
                                </svg>
                            </span>
                            <input type="email"
                                   id="si-client-email"
                                   name="email"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-phone" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Phone Number', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                </svg>
                            </span>
                            <input type="text"
                                   id="si-client-phone"
                                   name="phone"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field">
                        <label for="si-client-gst" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('GST Number', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                    <polyline points="14,2 14,8 20,8"></polyline>
                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                    <polyline points="10,9 9,9 8,9"></polyline>
                                </svg>
                            </span>
                            <input type="text"
                                   id="si-client-gst"
                                   name="gst_number"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Enter GST number (e.g., 22AAAAA0000A1Z5)', 'simple-invoice'); ?>" />
                        </div>
                    </div>

                    <div class="si-form-field si-field-full">
                        <label for="si-client-address" class="si-form-label">
                            <span class="si-label-text"><?php echo esc_html__('Address', 'simple-invoice'); ?></span>
                        </label>
                        <div class="si-input-wrapper">
                            <span class="si-input-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                                    <circle cx="12" cy="10" r="3"></circle>
                                </svg>
                            </span>
                            <textarea id="si-client-address"
                                      name="address"
                                      rows="3"
                                      class="si-form-textarea"
                                      placeholder="<?php echo esc_attr__('Client address', 'simple-invoice'); ?>"></textarea>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="si-modal-footer">
            <button type="button" class="si-btn si-btn-ghost si-modal-close">
                <span class="si-btn-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M18 6L6 18"></path>
                        <path d="M6 6l12 12"></path>
                    </svg>
                </span>
                <?php echo esc_html__('Cancel', 'simple-invoice'); ?>
            </button>
            <button type="button" class="si-btn si-btn-primary" id="si-save-client">
                <span class="si-btn-icon">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                        <polyline points="17,21 17,13 7,13 7,21"></polyline>
                        <polyline points="7,3 7,8 15,8"></polyline>
                    </svg>
                </span>
                <?php echo esc_html__('Save Client', 'simple-invoice'); ?>
            </button>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    var clientModal = $('#si-client-modal');
    var clientForm = $('#si-client-form');
    var isEditing = false;

    // Open add client modal
    $('#si-add-client-btn, #si-add-first-client').on('click', function(e) {
        e.preventDefault();
        openClientModal();
    });

    // Open edit client modal
    $(document).on('click', '.si-edit-client', function() {
        var clientId = $(this).data('client-id');
        openClientModal(clientId);
    });

    // Delete client
    $(document).on('click', '.si-delete-client', function() {
        var clientId = $(this).data('client-id');
        var clientRow = $(this).closest('tr');
        deleteClient(clientId, clientRow);
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeClientModal();
    });

    // Close modal when clicking backdrop
    $(document).on('click', '.si-modal', function(e) {
        if (e.target === this) {
            closeClientModal();
        }
    });

    // Save client
    $('#si-save-client').on('click', function() {
        saveClient();
    });

    function openClientModal(clientId) {
        isEditing = !!clientId;

        if (isEditing) {
            $('#si-client-modal-title').html('<span class="si-modal-icon"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg></span><?php echo esc_js(__('Edit Client', 'simple-invoice')); ?>');
            $('#si-save-client').html('<span class="si-btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17,21 17,13 7,13 7,21"></polyline><polyline points="7,3 7,8 15,8"></polyline></svg></span><?php echo esc_js(__('Update Client', 'simple-invoice')); ?>');
            loadClientData(clientId);
        } else {
            $('#si-client-modal-title').html('<span class="si-modal-icon"><svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></span><?php echo esc_js(__('Add New Client', 'simple-invoice')); ?>');
            $('#si-save-client').html('<span class="si-btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path><polyline points="17,21 17,13 7,13 7,21"></polyline><polyline points="7,3 7,8 15,8"></polyline></svg></span><?php echo esc_js(__('Save Client', 'simple-invoice')); ?>');
            clientForm[0].reset();
            $('#si-client-id').val('');
        }

        // Enhanced modal animation
        clientModal.css('display', 'flex');
        setTimeout(function() {
            clientModal.addClass('show');
        }, 10);

        // Focus first input
        setTimeout(function() {
            $('#si-client-name').focus();
        }, 300);
    }

    function closeClientModal() {
        clientModal.removeClass('show');
        setTimeout(function() {
            clientModal.hide();
            clientForm[0].reset();
            isEditing = false;
        }, 300);
    }

    function loadClientData(clientId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_get_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data) {
                    var client = response.data;
                    $('#si-client-id').val(client.id || '');
                    $('#si-client-name').val(client.name || '');
                    $('#si-client-business').val(client.business_name || '');
                    $('#si-client-email').val(client.email || '');
                    $('#si-client-phone').val(client.phone || client.contact_number || '');
                    $('#si-client-gst').val(client.gst_number || client.gstin || '');
                    $('#si-client-address').val(client.address || '');
                } else {
                    alert('<?php echo esc_js(__('Failed to load client data. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('Error loading client data. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

    function saveClient() {
        // Clear previous validation states
        $('.si-form-input, .si-form-textarea').removeClass('si-error si-success');

        var formData = {
            action: isEditing ? 'si_update_client' : 'si_add_client',
            nonce: '<?php echo wp_create_nonce('si_save_client_nonce'); ?>',
            client_id: $('#si-client-id').val(),
            name: $('#si-client-name').val().trim(),
            business_name: $('#si-client-business').val().trim(),
            email: $('#si-client-email').val().trim(),
            phone: $('#si-client-phone').val().trim(),
            gst_number: $('#si-client-gst').val().trim(),
            address: $('#si-client-address').val().trim()
        };

        // Enhanced validation with visual feedback
        var isValid = true;

        if (!formData.name) {
            $('#si-client-name').addClass('si-error').focus();
            showFieldError('#si-client-name', '<?php echo esc_js(__('Client name is required', 'simple-invoice')); ?>');
            isValid = false;
        } else {
            $('#si-client-name').addClass('si-success');
        }

        // Email validation
        if (formData.email && !isValidEmail(formData.email)) {
            $('#si-client-email').addClass('si-error');
            showFieldError('#si-client-email', '<?php echo esc_js(__('Please enter a valid email address', 'simple-invoice')); ?>');
            isValid = false;
        } else if (formData.email) {
            $('#si-client-email').addClass('si-success');
        }

        // GST validation (optional but if provided should be valid)
        if (formData.gst_number && !isValidGST(formData.gst_number)) {
            $('#si-client-gst').addClass('si-error');
            showFieldError('#si-client-gst', '<?php echo esc_js(__('Please enter a valid GST number (15 characters)', 'simple-invoice')); ?>');
            isValid = false;
        } else if (formData.gst_number) {
            $('#si-client-gst').addClass('si-success');
        }

        if (!isValid) {
            return;
        }

        // Show loading state
        var saveBtn = $('#si-save-client');
        var originalText = saveBtn.html();
        saveBtn.prop('disabled', true).html('<span class="si-btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"></circle><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"></path></svg></span>Saving...');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Show success state
                    saveBtn.html('<span class="si-btn-icon"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polyline points="20,6 9,17 4,12"></polyline></svg></span>Saved!');
                    setTimeout(function() {
                        closeClientModal();
                        location.reload();
                    }, 1000);
                } else {
                    saveBtn.prop('disabled', false).html(originalText);
                    showNotification(response.data || '<?php echo esc_js(__('Failed to save client. Please try again.', 'simple-invoice')); ?>', 'error');
                }
            },
            error: function() {
                saveBtn.prop('disabled', false).html(originalText);
                showNotification('<?php echo esc_js(__('Error saving client. Please try again.', 'simple-invoice')); ?>', 'error');
            }
        });
    }

    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidGST(gst) {
        // GST number should be 15 characters: 2 digits (state code) + 10 characters (PAN) + 1 character (entity number) + 1 character (Z) + 1 character (checksum)
        var gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
        return gstRegex.test(gst.toUpperCase());
    }

    function showFieldError(fieldSelector, message) {
        // Remove existing error messages
        $(fieldSelector).siblings('.si-field-error').remove();

        // Add error message
        $(fieldSelector).parent().append('<div class="si-field-error">' + message + '</div>');

        // Remove error message after 3 seconds
        setTimeout(function() {
            $(fieldSelector).siblings('.si-field-error').fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    function showNotification(message, type) {
        // Simple notification system
        var notification = $('<div class="si-notification si-notification-' + type + '">' + message + '</div>');
        $('body').append(notification);

        setTimeout(function() {
            notification.addClass('show');
        }, 100);

        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }

    function deleteClient(clientId, clientRow) {
        clientRow.css('opacity', '0.5');

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_delete_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    clientRow.fadeOut(300, function() {
                        $(this).remove();
                        if ($('.si-clients-table tbody tr').length === 0) {
                            location.reload(); // Reload to show empty state
                        }
                    });
                } else {
                    clientRow.css('opacity', '1');
                    alert(response.data || '<?php echo esc_js(__('Failed to delete client. Please try again.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                clientRow.css('opacity', '1');
                alert('<?php echo esc_js(__('Error deleting client. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }
});
</script>

<?php
// Include common footer
include WIMP_PLUGIN_PATH . 'admin/views/common/page-footer.php';
?>
