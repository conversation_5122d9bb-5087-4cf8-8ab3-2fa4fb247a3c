<?php
/**
 * Client Management Class
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SI_Client class for managing clients
 *
 * @since 1.0.0
 */
class SI_Client {

    /**
     * Table name
     *
     * @var string
     * @since 1.0.0
     */
    private $table_name;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'si_clients';
        
        // Initialize hooks
        $this->si_init_hooks();
    }

    /**
     * Initialize hooks
     *
     * @since 1.0.0
     */
    private function si_init_hooks() {
        add_action('wp_ajax_si_add_client', array($this, 'si_ajax_add_client'));
        add_action('wp_ajax_si_edit_client', array($this, 'si_ajax_edit_client'));
        add_action('wp_ajax_si_delete_client', array($this, 'si_ajax_delete_client'));
        add_action('wp_ajax_si_get_client', array($this, 'si_ajax_get_client'));
    }

    /**
     * Create a new client
     *
     * @param array $data Client data
     * @return int|false Client ID on success, false on failure
     * @since 1.0.0
     */
    public function si_create_client($data) {
        global $wpdb;

        // Sanitize data
        $sanitized_data = $this->si_sanitize_client_data($data);

        // Validate required fields
        if (empty($sanitized_data['name'])) {
            return false;
        }

        // Insert client
        $result = $wpdb->insert(
            $this->table_name,
            $sanitized_data,
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Update an existing client
     *
     * @param int $client_id Client ID
     * @param array $data Client data
     * @return bool True on success, false on failure
     * @since 1.0.0
     */
    public function si_update_client($client_id, $data) {
        global $wpdb;

        // Sanitize data
        $sanitized_data = $this->si_sanitize_client_data($data);

        // Validate required fields
        if (empty($sanitized_data['name'])) {
            return false;
        }

        // Update client
        $result = $wpdb->update(
            $this->table_name,
            $sanitized_data,
            array('id' => $client_id),
            array('%s', '%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Delete a client
     *
     * @param int $client_id Client ID
     * @return bool True on success, false on failure
     * @since 1.0.0
     */
    public function si_delete_client($client_id) {
        global $wpdb;

        // Check if client has invoices
        $invoice_table = $wpdb->prefix . 'si_invoices';
        $invoice_count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM $invoice_table WHERE client_id = %d",
                $client_id
            )
        );

        if ($invoice_count > 0) {
            return false; // Cannot delete client with existing invoices
        }

        // Delete client
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => $client_id),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Get client by ID
     *
     * @param int $client_id Client ID
     * @return object|null Client object or null if not found
     * @since 1.0.0
     */
    public function si_get_client($client_id) {
        global $wpdb;

        return $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM $this->table_name WHERE id = %d",
                $client_id
            )
        );
    }

    /**
     * Get all clients
     *
     * @param array $args Query arguments
     * @return array Array of client objects
     * @since 1.0.0
     */
    public function si_get_clients($args = array()) {
        global $wpdb;

        $defaults = array(
            'search' => '',
            'orderby' => 'name',
            'order' => 'ASC',
            'limit' => -1,
            'offset' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $sql = "SELECT * FROM $this->table_name";
        $where_conditions = array();

        // Add search condition
        if (!empty($args['search'])) {
            $search = '%' . $wpdb->esc_like($args['search']) . '%';
            $where_conditions[] = $wpdb->prepare(
                "(name LIKE %s OR business_name LIKE %s OR email LIKE %s)",
                $search, $search, $search
            );
        }

        // Add WHERE clause if conditions exist
        if (!empty($where_conditions)) {
            $sql .= " WHERE " . implode(' AND ', $where_conditions);
        }

        // Add ORDER BY clause
        $sql .= " ORDER BY " . esc_sql($args['orderby']) . " " . esc_sql($args['order']);

        // Add LIMIT clause
        if ($args['limit'] > 0) {
            $sql .= " LIMIT " . intval($args['offset']) . ", " . intval($args['limit']);
        }

        return $wpdb->get_results($sql);
    }

    /**
     * Sanitize client data
     *
     * @param array $data Raw client data
     * @return array Sanitized client data
     * @since 1.0.0
     */
    private function si_sanitize_client_data($data) {
        return array(
            'name' => si_sanitize_text($data['name'] ?? ''),
            'business_name' => si_sanitize_text($data['business_name'] ?? ''),
            'address' => si_sanitize_textarea($data['address'] ?? ''),
            'phone' => si_sanitize_text($data['phone'] ?? $data['contact_number'] ?? ''),
            'email' => si_sanitize_email($data['email'] ?? ''),
            'gst_number' => si_sanitize_text($data['gst_number'] ?? $data['gstin'] ?? '')
        );
    }

    /**
     * AJAX handler for adding client
     *
     * @since 1.0.0
     */
    public function si_ajax_add_client() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_add_client_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        // Create client
        $client_id = $this->si_create_client($_POST);

        if ($client_id) {
            $client = $this->si_get_client($client_id);
            si_send_json_response(
                true,
                __('Client added successfully.', 'simple-invoice'),
                array('client' => $client)
            );
        } else {
            si_send_json_response(false, __('Failed to add client.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for editing client
     *
     * @since 1.0.0
     */
    public function si_ajax_edit_client() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_edit_client_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);

        if (!$client_id) {
            si_send_json_response(false, __('Invalid client ID.', 'simple-invoice'));
        }

        // Update client
        $success = $this->si_update_client($client_id, $_POST);

        if ($success) {
            $client = $this->si_get_client($client_id);
            si_send_json_response(
                true,
                __('Client updated successfully.', 'simple-invoice'),
                array('client' => $client)
            );
        } else {
            si_send_json_response(false, __('Failed to update client.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for deleting client
     *
     * @since 1.0.0
     */
    public function si_ajax_delete_client() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_delete_client_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);

        if (!$client_id) {
            si_send_json_response(false, __('Invalid client ID.', 'simple-invoice'));
        }

        // Delete client
        $success = $this->si_delete_client($client_id);

        if ($success) {
            si_send_json_response(true, __('Client deleted successfully.', 'simple-invoice'));
        } else {
            si_send_json_response(false, __('Failed to delete client. Client may have existing invoices.', 'simple-invoice'));
        }
    }

    /**
     * AJAX handler for getting client data
     *
     * @since 1.0.0
     */
    public function si_ajax_get_client() {
        // Verify nonce
        if (!si_verify_ajax_nonce('si_get_client_nonce')) {
            si_send_json_response(false, __('Security check failed.', 'simple-invoice'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            si_send_json_response(false, __('Insufficient permissions.', 'simple-invoice'));
        }

        $client_id = intval($_POST['client_id'] ?? 0);

        if (!$client_id) {
            si_send_json_response(false, __('Invalid client ID.', 'simple-invoice'));
        }

        $client = $this->si_get_client($client_id);

        if ($client) {
            si_send_json_response(
                true,
                __('Client data retrieved.', 'simple-invoice'),
                array('client' => $client)
            );
        } else {
            si_send_json_response(false, __('Client not found.', 'simple-invoice'));
        }
    }
}
